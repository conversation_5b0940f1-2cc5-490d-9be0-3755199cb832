package com.job.jobportal.config;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;

@Component
public class DatabaseInitializer {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private Environment environment;

    @PostConstruct
    public void initializeDatabase() throws Exception {
        // Check if initialization is enabled
        boolean isInitializationEnabled = Boolean.parseBoolean(
                environment.getProperty("app.database.initialize", "false")
        );

        if (!isInitializationEnabled) {
            System.out.println("Database initialization skipped.");
            return;
        }

        Resource resource = resourceLoader.getResource("classpath:schema.sql");
        String sql = new String(Files.readAllBytes(Paths.get(resource.getURI())));

        // Split SQL script using custom delimiter $$
        String[] statements = sql.split("\\$\\$");

        for (String statement : statements) {
            if (!statement.trim().isEmpty()) {
                System.out.println("------------");
                System.out.println(statement.trim());
                System.out.println("------------");
                jdbcTemplate.execute(statement.trim());
            }
        }
    }
}
