package com.job.jobportal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionPlanDTO {
    private Long planId;
    private String planName;
    private String planObject; // Stripe price ID
    private String permissions;
    private boolean isActive;
    private String billingCycle; // "Monthly" or "Yearly"
    private String baseType; // "Standard", "Premium", "Enterprise", or "Trial"
}
