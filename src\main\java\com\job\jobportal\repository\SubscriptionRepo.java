package com.job.jobportal.repository;

import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@Transactional
public interface SubscriptionRepo extends JpaRepository<Subscription, Long> {

    Optional<Subscription> findByRegistereduser(Registereduser registereduser);

    @Query("SELECT s FROM Subscription s WHERE s.registereduser.userid = :userId")
    Optional<Subscription> findByRegistereduser_Userid(@Param("userId") Long userId);

    @Modifying
    @Query("Update Subscription set subscriptionStatus=:subscriptionStatus  where subscriptionAccountId=:subscriptionAccountId")
    int updateSubscriptionStatus(@Param("subscriptionStatus") int subscriptionStatus,
                                 @Param("subscriptionAccountId") String subscriptionAccountId
    );
}
