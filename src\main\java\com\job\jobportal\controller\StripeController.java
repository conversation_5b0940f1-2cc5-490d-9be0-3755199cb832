package com.job.jobportal.controller;

import com.job.jobportal.dto.ChangePlanRequestDTO;
import com.job.jobportal.dto.CheckoutSessionResponseDTO;
import com.job.jobportal.dto.InvoiceListResponseDTO;
import com.job.jobportal.dto.SubscriptionPlanDTO;
import com.job.jobportal.dto.SubscriptionRequestDTO;
import com.job.jobportal.dto.SubscriptionResponseDTO;
import com.job.jobportal.dto.TrialSubscriptionResponseDTO;
import com.job.jobportal.dto.UpdatePaymentMethodRequestDTO;
import com.job.jobportal.dto.UpdatePaymentMethodResponseDTO;
import com.job.jobportal.model.PaymentMethod;
import com.job.jobportal.repository.PaymentMethodRepository;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.service.StripeService;
import com.stripe.exception.StripeException;
import com.stripe.model.Event;
import com.stripe.net.Webhook;
import com.stripe.exception.SignatureVerificationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/stripe")
public class StripeController {

    private static final Logger logger = LoggerFactory.getLogger(StripeController.class);

    @Autowired
    private StripeService stripeService;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private PaymentMethodRepository paymentMethodRepository;

    @Value("${stripe.webhook.signing.secret}")
    private String webhookSecret;

    @PreAuthorize("hasRole('RECRUITER')")
    @PostMapping("/create-checkout-session")
    public ResponseEntity<?> createCheckoutSession(@Valid @RequestBody SubscriptionRequestDTO request) {
        try {
            CheckoutSessionResponseDTO session = stripeService.createCheckoutSession(
                    request.getPlanName(),
                    request.isYearly()
            );

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.checkout.session.created",
                        null,
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException e) {
                successMessage = "Checkout session created successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    session,
                    successMessage
            ));
        } catch (BadRequestException e) {
            logger.error("Bad request during checkout session creation: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (StripeException e) {
            logger.error("Error creating checkout session: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    "Failed to create checkout session"
            ));
        }
    }

    @PostMapping("/webhook")
    public ResponseEntity<?> handleWebhook(@RequestBody String payload, HttpServletRequest request) {
        String sigHeader = request.getHeader("Stripe-Signature");
        if (sigHeader == null) {
            logger.error("Missing Stripe signature header");
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    "Missing Stripe signature header"
            ));
        }

        try {
            Event event = Webhook.constructEvent(payload, sigHeader, webhookSecret);
            logger.info("""
                    Processing Stripe webhook event: {}
                    """.trim(), event.getType());

            stripeService.handleWebhookEvent(event);

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    null,
                    """
                            Webhook processed successfully
                            """.trim()
            ));
        } catch (SignatureVerificationException e) {
            logger.error("""
                    Invalid Stripe webhook signature: {}
                    """.trim(), e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    """
                            Invalid signature
                            """.trim()
            ));
        } catch (BadRequestException e) {
            logger.error("""
                    Bad request in webhook: {}
                    """.trim(), e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (Exception e) {
            logger.error("""
                    Error processing webhook: {}
                    """.trim(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    """
                            Internal server error while processing webhook
                            """.trim()
            ));
        }
    }

    @PreAuthorize("hasRole('RECRUITER')")
    @PutMapping("/subscription/plan")
    public ResponseEntity<?> changePlan() {
        try {
            CheckoutSessionResponseDTO session = stripeService.changePlan();

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.change.plan.session.created",
                        null,
                        "Plan change checkout session created successfully",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException e) {
                successMessage = "Plan change checkout session created successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    session,
                    successMessage
            ));
        } catch (BadRequestException e) {
            logger.error("Bad request during plan change session creation: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (StripeException e) {
            logger.error("Error creating plan change session: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    "Failed to create plan change session"
            ));
        }
    }

    @PreAuthorize("hasRole('RECRUITER')")
    @GetMapping("/subscription")
    public ResponseEntity<?> getSubscriptionDetails() {
        try {
            Object subscription = stripeService.getCurrentSubscriptionDetails();

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.subscription.details.fetched",
                        null,
                        "Subscription details retrieved successfully",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException e) {
                successMessage = "Subscription details retrieved successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    subscription,
                    successMessage
            ));
        } catch (BadRequestException e) {
            logger.error("Bad request fetching subscription: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (Exception e) {
            logger.error("Error fetching subscription details: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "msg.something_went_wrong",
                        null,
                        "Something went wrong while fetching subscription details",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException ex) {
                errorMessage = "Something went wrong while fetching subscription details";
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage
            ));
        }
    }

    @PreAuthorize("hasRole('RECRUITER')")
    @PutMapping("/subscription")
    public ResponseEntity<?> cancelSubscription() {
        try {
            SubscriptionResponseDTO response = stripeService.cancelCurrentSubscription();

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.subscription.cancelled",
                        null,
                        "Subscription cancelled successfully",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException e) {
                successMessage = "Subscription cancelled successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    response,
                    successMessage
            ));
        } catch (BadRequestException e) {
            logger.error("Bad request cancelling subscription: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (Exception e) {
            logger.error("Error cancelling subscription: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "msg.something_went_wrong",
                        null,
                        "Something went wrong while cancelling the subscription",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException ex) {
                errorMessage = "Something went wrong while cancelling the subscription";
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage
            ));
        }
    }

    @PreAuthorize("hasRole('RECRUITER')")
    @PostMapping("/trial")
    public ResponseEntity<?> activateTrialSubscription() {
        try {
            TrialSubscriptionResponseDTO trialSubscription = stripeService.activateTrialSubscription();

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.trial.activated",
                        null,
                        "Trial subscription activated successfully",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException e) {
                successMessage = "Trial subscription activated successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    trialSubscription,
                    successMessage
            ));
        } catch (BadRequestException e) {
            logger.error("Bad request activating trial: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (Exception e) {
            logger.error("Error activating trial subscription: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "msg.something_went_wrong",
                        null,
                        "Something went wrong while activating the trial subscription",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException ex) {
                errorMessage = "Something went wrong while activating the trial subscription";
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage
            ));
        }
    }

    @PreAuthorize("hasRole('RECRUITER')")
    @GetMapping("/invoices")
    public ResponseEntity<?> getCustomerInvoices(
            @RequestParam(name = "limit", defaultValue = "10") int limit) {
        try {
            InvoiceListResponseDTO invoices = stripeService.getCustomerInvoices(limit);

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.invoices.fetched",
                        null,
                        "Invoices retrieved successfully",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException e) {
                successMessage = "Invoices retrieved successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    invoices,
                    successMessage
            ));
        } catch (BadRequestException e) {
            logger.error("Bad request fetching invoices: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (StripeException e) {
            logger.error("Stripe error fetching invoices: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "msg.stripe.error",
                        null,
                        "Error communicating with Stripe",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException ex) {
                errorMessage = "Error communicating with Stripe";
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage
            ));
        } catch (Exception e) {
            logger.error("Error fetching invoices: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "msg.something_went_wrong",
                        null,
                        "Something went wrong while fetching invoices",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException ex) {
                errorMessage = "Something went wrong while fetching invoices";
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage
            ));
        }
    }

    @GetMapping("/subscription/plans")
    public ResponseEntity<?> getAllSubscriptionDetails() {
        try {
            List<SubscriptionPlanDTO> plans = stripeService.getAllSubscriptionDetails();

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.subscription.plans.fetched",
                        null,
                        "Subscription plans retrieved successfully",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException e) {
                successMessage = "Subscription plans retrieved successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    plans,
                    successMessage
            ));
        } catch (BadRequestException e) {
            logger.error("Bad request fetching subscription plans: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()
            ));
        } catch (Exception e) {
            logger.error("Error fetching subscription plans: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "msg.something_went_wrong",
                        null,
                        "Something went wrong while fetching subscription plans",
                        LocaleContextHolder.getLocale()
                );
            } catch (NoSuchMessageException ex) {
                errorMessage = "Something went wrong while fetching subscription plans";
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage
            ));
        }
    }

    @GetMapping("/payment-methods")
    public ResponseEntity<?> getAvailablePaymentMethods() {
        try {
            List<PaymentMethod> paymentMethods = paymentMethodRepository.findByActiveTrue();

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.payment.methods.fetched",
                        null,
                        "Payment methods retrieved successfully",
                        LocaleContextHolder.getLocale());
            } catch (NoSuchMessageException e) {
                successMessage = "Payment methods retrieved successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    paymentMethods,
                    successMessage));
        } catch (Exception e) {
            logger.error("Error fetching payment methods: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "error.payment.methods.fetch.failed",
                        null,
                        "Failed to retrieve payment methods",
                        LocaleContextHolder.getLocale());
            } catch (NoSuchMessageException ex) {
                errorMessage = "Failed to retrieve payment methods";
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage));
        }
    }

    @PreAuthorize("hasRole('RECRUITER')")
    @PostMapping("/payment-method")
    public ResponseEntity<?> updatePaymentMethod(@Valid @RequestBody UpdatePaymentMethodRequestDTO request) {
        try {
            UpdatePaymentMethodResponseDTO response = stripeService.updatePaymentMethod(request.getPaymentMethodId());

            String successMessage;
            try {
                successMessage = messageSource.getMessage(
                        "msg.payment.method.updated",
                        null,
                        "Payment method updated successfully",
                        LocaleContextHolder.getLocale());
            } catch (NoSuchMessageException e) {
                successMessage = "Payment method updated successfully";
            }

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    response,
                    successMessage));
        } catch (BadRequestException e) {
            logger.error("Bad request updating payment method: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()));
        } catch (StripeException e) {
            logger.error("Stripe error updating payment method: {}", e.getMessage());

            String errorMessage;
            try {
                errorMessage = messageSource.getMessage(
                        "error.payment.method.update.failed",
                        null,
                        "Failed to update payment method",
                        LocaleContextHolder.getLocale());
            } catch (NoSuchMessageException ex) {
                errorMessage = "Failed to update payment method: " + e.getMessage();
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    errorMessage));
        }
    }

    @PreAuthorize("hasRole('RECRUITER')")
    @PostMapping("/test-basic-portal")
    public ResponseEntity<?> testBasicPortal() {
        try {
            Map<String, Object> response = stripeService.createBasicCustomerPortalSession();

            return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK,
                    true,
                    response,
                    "Basic customer portal session created successfully"));

        } catch (BadRequestException e) {
            logger.error("Bad request creating basic portal session: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                    HttpStatus.BAD_REQUEST,
                    false,
                    null,
                    e.getMessage()));
        } catch (StripeException e) {
            logger.error("Stripe error creating basic portal session: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    false,
                    null,
                    "Failed to create basic portal session: " + e.getMessage()));
        }
    }
}